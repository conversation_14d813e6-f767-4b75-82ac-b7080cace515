#!/bin/bash

# Price Tracker Cron Setup Script
# This script helps set up automated daily price checking

echo "Price Tracker - Cron Setup"
echo "=========================="

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_PATH=$(which python3)

echo "Script directory: $SCRIPT_DIR"
echo "Python path: $PYTHON_PATH"

# Create the cron command
CRON_COMMAND="0 9 * * * cd $SCRIPT_DIR && $PYTHON_PATH main.py >> price_tracker_cron.log 2>&1"

echo ""
echo "Proposed cron job (runs daily at 9:00 AM):"
echo "$CRON_COMMAND"
echo ""

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "main.py"; then
    echo "⚠️  A cron job for main.py already exists!"
    echo "Current cron jobs:"
    crontab -l | grep "main.py"
    echo ""
    read -p "Do you want to replace it? (y/N): " replace
    if [[ $replace =~ ^[Yy]$ ]]; then
        # Remove existing cron job
        crontab -l | grep -v "main.py" | crontab -
        echo "Existing cron job removed."
    else
        echo "Setup cancelled."
        exit 0
    fi
fi

# Add the new cron job
echo "Adding cron job..."
(crontab -l 2>/dev/null; echo "$CRON_COMMAND") | crontab -

if [ $? -eq 0 ]; then
    echo "✅ Cron job added successfully!"
    echo ""
    echo "The price tracker will now run daily at 9:00 AM."
    echo "Logs will be written to: $SCRIPT_DIR/price_tracker_cron.log"
    echo ""
    echo "To view current cron jobs: crontab -l"
    echo "To remove the cron job: crontab -e (then delete the line)"
    echo ""
    echo "You can test the setup by running:"
    echo "  python3 main.py --test"
else
    echo "❌ Failed to add cron job!"
    exit 1
fi
