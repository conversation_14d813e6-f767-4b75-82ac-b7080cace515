#!/usr/bin/env python3
"""
Main entry point for the Price Tracker application.
"""

import sys
import argparse
import logging
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.price_tracker import PriceTracker

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('price_tracker.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Price Tracker with Telegram Notifications')
    parser.add_argument(
        '--config',
        default='config.json',
        help='Path to configuration file (default: config.json)'
    )
    parser.add_argument(
        '--test',
        action='store_true',
        help='Test configuration and components'
    )
    parser.add_argument(
        '--no-summary',
        action='store_true',
        help='Skip sending summary notification'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialize price tracker
        logger.info("Initializing Price Tracker")
        tracker = PriceTracker(args.config)
        
        if args.test:
            # Run configuration test
            logger.info("Running configuration test")
            test_results = tracker.test_configuration()
            
            print("\n" + "="*50)
            print("CONFIGURATION TEST RESULTS")
            print("="*50)
            
            print(f"Configuration Valid: {'✅' if test_results['config_valid'] else '❌'}")
            print(f"Database OK: {'✅' if test_results['database_ok'] else '❌'}")
            print(f"Telegram OK: {'✅' if test_results['telegram_ok'] else '❌'}")
            print(f"Scraper OK: {'✅' if test_results['scraper_ok'] else '❌'}")
            print(f"Overall Status: {'✅' if test_results['overall_ok'] else '❌'}")
            
            if 'products_in_db' in test_results:
                print(f"Products in Database: {test_results['products_in_db']}")
            
            if 'bot_info' in test_results:
                bot_info = test_results['bot_info']
                print(f"Bot Username: @{bot_info.get('bot_username', 'N/A')}")
                print(f"Bot Name: {bot_info.get('bot_first_name', 'N/A')}")
            
            # Show errors if any
            for key, value in test_results.items():
                if key.endswith('_error'):
                    component = key.replace('_error', '').title()
                    print(f"{component} Error: {value}")
            
            if 'config_errors' in test_results:
                print("Configuration Errors:")
                for error in test_results['config_errors']:
                    print(f"  - {error}")
            
            print("="*50)
            
            if test_results['overall_ok']:
                print("✅ All tests passed! Your price tracker is ready to use.")
                
                # Send test message
                print("\nSending test Telegram message...")
                if tracker.notifier.send_test_message():
                    print("✅ Test message sent successfully!")
                else:
                    print("❌ Failed to send test message")
            else:
                print("❌ Some tests failed. Please check the configuration.")
                sys.exit(1)
        
        else:
            # Run price check
            logger.info("Starting price check")
            
            send_summary = not args.no_summary
            results = tracker.run_price_check(send_summary=send_summary)
            
            print("\n" + "="*50)
            print("PRICE CHECK RESULTS")
            print("="*50)
            print(f"Products Checked: {results['products_checked']}")
            print(f"Price Drops Found: {results['price_drops_found']}")
            print(f"Errors: {results['errors']}")
            
            if results['duration_seconds']:
                print(f"Duration: {results['duration_seconds']:.1f} seconds")
            
            print("="*50)
            
            if results['price_drops_found'] > 0:
                print(f"🔥 Found {results['price_drops_found']} price drop(s)! Check your Telegram for details.")
            elif results['errors'] > 0:
                print(f"⚠️ Completed with {results['errors']} error(s). Check the logs for details.")
            else:
                print("✅ All products checked successfully. No price drops found.")
    
    except KeyboardInterrupt:
        logger.info("Price tracker interrupted by user")
        print("\nPrice tracker stopped by user.")
        sys.exit(0)
    
    except Exception as e:
        logger.error(f"Price tracker failed: {e}")
        print(f"\n❌ Price tracker failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
