"""
Configuration management module for the price tracker application.
"""

import json
import os
import logging
from typing import Dict, Any, List, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Config:
    """Configuration manager for the price tracker application."""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.config_data = {}
        self.load_config()
    
    def load_config(self) -> bool:
        """
        Load configuration from JSON file.
        
        Returns:
            True if config loaded successfully, False otherwise
        """
        try:
            if not os.path.exists(self.config_path):
                logger.error(f"Configuration file not found: {self.config_path}")
                return False
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = json.load(f)
            
            logger.info(f"Configuration loaded from {self.config_path}")
            return True
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        Save current configuration to JSON file.
        
        Returns:
            True if config saved successfully, False otherwise
        """
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration saved to {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def get_telegram_config(self) -> Dict[str, str]:
        """Get Telegram bot configuration."""
        telegram_config = self.config_data.get('telegram', {})
        
        return {
            'bot_token': telegram_config.get('bot_token', ''),
            'chat_id': telegram_config.get('chat_id', '')
        }
    
    def get_database_config(self) -> Dict[str, str]:
        """Get database configuration."""
        db_config = self.config_data.get('database', {})
        
        return {
            'path': db_config.get('path', 'price_tracker.db')
        }
    
    def get_products(self) -> List[Dict[str, Any]]:
        """Get list of products to track."""
        return self.config_data.get('products', [])
    
    def add_product(self, name: str, url: str, price_selector: str, currency: str = "€") -> bool:
        """
        Add a new product to the configuration.
        
        Args:
            name: Product name
            url: Product URL
            price_selector: CSS selector for price element
            currency: Currency symbol
            
        Returns:
            True if product added successfully, False otherwise
        """
        try:
            if 'products' not in self.config_data:
                self.config_data['products'] = []
            
            # Check if product with same URL already exists
            for product in self.config_data['products']:
                if product.get('url') == url:
                    logger.warning(f"Product with URL {url} already exists")
                    return False
            
            new_product = {
                'name': name,
                'url': url,
                'price_selector': price_selector,
                'currency': currency
            }
            
            self.config_data['products'].append(new_product)
            
            logger.info(f"Added product to configuration: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add product: {e}")
            return False
    
    def remove_product(self, url: str) -> bool:
        """
        Remove a product from the configuration.
        
        Args:
            url: Product URL to remove
            
        Returns:
            True if product removed successfully, False otherwise
        """
        try:
            if 'products' not in self.config_data:
                return False
            
            original_count = len(self.config_data['products'])
            self.config_data['products'] = [
                product for product in self.config_data['products']
                if product.get('url') != url
            ]
            
            if len(self.config_data['products']) < original_count:
                logger.info(f"Removed product with URL: {url}")
                return True
            else:
                logger.warning(f"Product with URL {url} not found")
                return False
                
        except Exception as e:
            logger.error(f"Failed to remove product: {e}")
            return False
    
    def update_telegram_config(self, bot_token: str, chat_id: str) -> bool:
        """
        Update Telegram configuration.
        
        Args:
            bot_token: Telegram bot token
            chat_id: Telegram chat ID
            
        Returns:
            True if updated successfully, False otherwise
        """
        try:
            if 'telegram' not in self.config_data:
                self.config_data['telegram'] = {}
            
            self.config_data['telegram']['bot_token'] = bot_token
            self.config_data['telegram']['chat_id'] = chat_id
            
            logger.info("Telegram configuration updated")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update Telegram configuration: {e}")
            return False
    
    def validate_config(self) -> Dict[str, Any]:
        """
        Validate the current configuration.
        
        Returns:
            Dictionary with validation results
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check Telegram configuration
        telegram_config = self.get_telegram_config()
        if not telegram_config['bot_token']:
            validation_result['errors'].append("Telegram bot token is missing")
            validation_result['valid'] = False
        
        if not telegram_config['chat_id']:
            validation_result['errors'].append("Telegram chat ID is missing")
            validation_result['valid'] = False
        
        # Check products
        products = self.get_products()
        if not products:
            validation_result['warnings'].append("No products configured for tracking")
        
        for i, product in enumerate(products):
            if not product.get('name'):
                validation_result['errors'].append(f"Product {i+1}: name is missing")
                validation_result['valid'] = False
            
            if not product.get('url'):
                validation_result['errors'].append(f"Product {i+1}: URL is missing")
                validation_result['valid'] = False
            
            if not product.get('price_selector'):
                validation_result['errors'].append(f"Product {i+1}: price selector is missing")
                validation_result['valid'] = False
        
        return validation_result
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration."""
        telegram_config = self.get_telegram_config()
        products = self.get_products()
        
        return {
            'telegram_configured': bool(telegram_config['bot_token'] and telegram_config['chat_id']),
            'products_count': len(products),
            'database_path': self.get_database_config()['path'],
            'config_file': self.config_path
        }
