"""
Telegram notification module for sending price drop alerts.
"""

import requests
import logging
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TelegramNotifier:
    """Handles sending notifications via Telegram Bot API."""
    
    def __init__(self, bot_token: str, chat_id: str):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
    
    def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """
        Send a message via Telegram.
        
        Args:
            message: Message text to send
            parse_mode: Message formatting (HTML or Markdown)
            
        Returns:
            True if message sent successfully, False otherwise
        """
        try:
            url = f"{self.base_url}/sendMessage"
            
            payload = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": parse_mode,
                "disable_web_page_preview": False
            }
            
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("ok"):
                logger.info("Telegram message sent successfully")
                return True
            else:
                logger.error(f"Telegram API error: {result.get('description', 'Unknown error')}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending Telegram message: {e}")
            return False
    
    def send_price_drop_alert(self, drop_info: Dict[str, Any]) -> bool:
        """
        Send a formatted price drop alert.
        
        Args:
            drop_info: Dictionary containing price drop information
            
        Returns:
            True if alert sent successfully, False otherwise
        """
        try:
            product_name = drop_info['product_name']
            old_price = drop_info['old_price']
            new_price = drop_info['new_price']
            price_drop = drop_info['price_drop']
            drop_percentage = drop_info['drop_percentage']
            product_url = drop_info['product_url']
            
            # Create formatted message
            message = f"""
🔥 <b>Price Drop Alert!</b> 🔥

📦 <b>Product:</b> {product_name}

💰 <b>Price Change:</b>
   • Old Price: €{old_price:.2f}
   • New Price: €{new_price:.2f}
   • You Save: €{price_drop:.2f} ({drop_percentage:.1f}% off)

🔗 <a href="{product_url}">View Product</a>

⏰ Alert sent at: {self._get_current_time()}
            """.strip()
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"Failed to format price drop alert: {e}")
            return False
    
    def send_test_message(self) -> bool:
        """Send a test message to verify the bot is working."""
        message = """
🤖 <b>Price Tracker Test</b>

This is a test message from your Price Tracker bot.

✅ If you received this message, your bot is configured correctly!

⏰ Test sent at: {}
        """.format(self._get_current_time()).strip()
        
        return self.send_message(message)
    
    def send_daily_summary(self, products_checked: int, price_drops: int, errors: int) -> bool:
        """
        Send a daily summary of price tracking activity.
        
        Args:
            products_checked: Number of products checked
            price_drops: Number of price drops detected
            errors: Number of errors encountered
            
        Returns:
            True if summary sent successfully, False otherwise
        """
        try:
            if price_drops > 0:
                emoji = "🔥"
                status = f"Found {price_drops} price drop(s)!"
            elif errors > 0:
                emoji = "⚠️"
                status = f"Completed with {errors} error(s)"
            else:
                emoji = "✅"
                status = "All products checked successfully"
            
            message = f"""
{emoji} <b>Daily Price Check Summary</b>

📊 <b>Statistics:</b>
   • Products Checked: {products_checked}
   • Price Drops Found: {price_drops}
   • Errors: {errors}

📈 <b>Status:</b> {status}

⏰ Summary for: {self._get_current_time()}
            """.strip()
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"Failed to send daily summary: {e}")
            return False
    
    def _get_current_time(self) -> str:
        """Get current time formatted for display."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test the Telegram bot connection and return bot information.
        
        Returns:
            Dictionary with connection test results
        """
        try:
            url = f"{self.base_url}/getMe"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("ok"):
                bot_info = result.get("result", {})
                return {
                    "success": True,
                    "bot_username": bot_info.get("username"),
                    "bot_first_name": bot_info.get("first_name"),
                    "bot_id": bot_info.get("id")
                }
            else:
                return {
                    "success": False,
                    "error": result.get("description", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
