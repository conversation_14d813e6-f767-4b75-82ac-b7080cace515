"""
Database module for storing product prices and tracking price history.
"""

import sqlite3
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from contextlib import contextmanager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PriceDatabase:
    """SQLite database manager for price tracking."""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Products table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    url TEXT UNIQUE NOT NULL,
                    price_selector TEXT NOT NULL,
                    currency TEXT DEFAULT '€',
                    current_price REAL,
                    last_updated TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Price history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER,
                    price REAL NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')
            
            # Price alerts table (for tracking when notifications were sent)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER,
                    old_price REAL,
                    new_price REAL,
                    price_drop REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')
            
            conn.commit()
            logger.info("Database initialized successfully")
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access to rows
        try:
            yield conn
        finally:
            conn.close()
    
    def add_product(self, name: str, url: str, price_selector: str, currency: str = "€") -> int:
        """
        Add a new product to track.
        
        Args:
            name: Product name
            url: Product URL
            price_selector: CSS selector for price element
            currency: Currency symbol
            
        Returns:
            Product ID
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO products 
                (name, url, price_selector, currency)
                VALUES (?, ?, ?, ?)
            ''', (name, url, price_selector, currency))
            
            product_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Added product: {name} (ID: {product_id})")
            return product_id
    
    def get_all_products(self) -> List[Dict[str, Any]]:
        """Get all products from the database."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM products ORDER BY name')
            
            products = []
            for row in cursor.fetchall():
                products.append(dict(row))
            
            return products
    
    def get_product_by_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Get product by URL."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM products WHERE url = ?', (url,))
            
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def update_product_price(self, product_id: int, new_price: float) -> bool:
        """
        Update product price and add to history.
        
        Args:
            product_id: Product ID
            new_price: New price value
            
        Returns:
            True if price was different and updated, False if same price
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Get current price
            cursor.execute('SELECT current_price FROM products WHERE id = ?', (product_id,))
            row = cursor.fetchone()
            
            if not row:
                logger.error(f"Product with ID {product_id} not found")
                return False
            
            current_price = row['current_price']
            
            # Update product price
            cursor.execute('''
                UPDATE products 
                SET current_price = ?, last_updated = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_price, product_id))
            
            # Add to price history
            cursor.execute('''
                INSERT INTO price_history (product_id, price)
                VALUES (?, ?)
            ''', (product_id, new_price))
            
            conn.commit()
            
            # Check if price changed
            price_changed = current_price is None or abs(current_price - new_price) > 0.01
            
            if price_changed:
                logger.info(f"Price updated for product {product_id}: {current_price} -> {new_price}")
            
            return price_changed
    
    def check_price_drop(self, product_id: int, new_price: float) -> Optional[Dict[str, Any]]:
        """
        Check if there's a price drop and return drop information.
        
        Args:
            product_id: Product ID
            new_price: New price to compare
            
        Returns:
            Dictionary with price drop info or None if no drop
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Get current price and product info
            cursor.execute('''
                SELECT id, name, current_price, url 
                FROM products 
                WHERE id = ?
            ''', (product_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            product = dict(row)
            current_price = product['current_price']
            
            # Check if there's a price drop
            if current_price is not None and new_price < current_price:
                price_drop = current_price - new_price
                drop_percentage = (price_drop / current_price) * 100
                
                drop_info = {
                    'product_id': product_id,
                    'product_name': product['name'],
                    'product_url': product['url'],
                    'old_price': current_price,
                    'new_price': new_price,
                    'price_drop': price_drop,
                    'drop_percentage': drop_percentage
                }
                
                logger.info(f"Price drop detected for {product['name']}: {current_price} -> {new_price} (-{price_drop:.2f})")
                return drop_info
            
            return None
    
    def record_price_alert(self, product_id: int, old_price: float, new_price: float):
        """Record that a price alert was sent."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            price_drop = old_price - new_price
            
            cursor.execute('''
                INSERT INTO price_alerts (product_id, old_price, new_price, price_drop)
                VALUES (?, ?, ?, ?)
            ''', (product_id, old_price, new_price, price_drop))
            
            conn.commit()
            logger.info(f"Recorded price alert for product {product_id}")
    
    def get_price_history(self, product_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get price history for a product."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT price, timestamp 
                FROM price_history 
                WHERE product_id = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            ''', (product_id, limit))
            
            history = []
            for row in cursor.fetchall():
                history.append(dict(row))
            
            return history
