#!/usr/bin/env python3
"""
Interactive script to help set up Telegram bot configuration.
"""

import json
import requests
import sys
from pathlib import Path

def get_bot_info(bot_token):
    """Get bot information to verify the token."""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        if result.get("ok"):
            return result.get("result", {})
        else:
            print(f"❌ Error: {result.get('description', 'Unknown error')}")
            return None
    except Exception as e:
        print(f"❌ Failed to verify bot token: {e}")
        return None

def get_chat_id(bot_token):
    """Get recent updates to find chat ID."""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        if result.get("ok"):
            updates = result.get("result", [])
            if updates:
                # Get the most recent chat ID
                latest_update = updates[-1]
                chat = latest_update.get("message", {}).get("chat", {})
                return chat.get("id")
            else:
                return None
        else:
            print(f"❌ Error getting updates: {result.get('description', 'Unknown error')}")
            return None
    except Exception as e:
        print(f"❌ Failed to get updates: {e}")
        return None

def update_config(bot_token, chat_id):
    """Update the configuration file with Telegram settings."""
    config_path = "config.json"
    
    try:
        # Load existing config
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Update Telegram settings
        config["telegram"]["bot_token"] = bot_token
        config["telegram"]["chat_id"] = str(chat_id)
        
        # Save updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Configuration updated in {config_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update configuration: {e}")
        return False

def main():
    print("🤖 Telegram Bot Setup for Price Tracker")
    print("="*50)
    print()
    print("Before running this script, you need to:")
    print("1. Go to Telegram and search for 'BotFather'")
    print("2. Send /newbot and follow the instructions")
    print("3. Copy the bot token you receive")
    print("4. Send any message to your new bot")
    print()
    
    # Get bot token
    bot_token = input("Enter your bot token: ").strip()
    
    if not bot_token or bot_token == "YOUR_BOT_TOKEN_HERE":
        print("❌ Please provide a valid bot token")
        sys.exit(1)
    
    # Verify bot token
    print("\n🔍 Verifying bot token...")
    bot_info = get_bot_info(bot_token)
    
    if not bot_info:
        print("❌ Invalid bot token or connection failed")
        sys.exit(1)
    
    print(f"✅ Bot verified!")
    print(f"   Bot Name: {bot_info.get('first_name', 'N/A')}")
    print(f"   Username: @{bot_info.get('username', 'N/A')}")
    
    # Get chat ID
    print("\n🔍 Looking for your chat ID...")
    print("Make sure you've sent at least one message to your bot!")
    
    chat_id = get_chat_id(bot_token)
    
    if not chat_id:
        print("❌ No chat ID found!")
        print("Please:")
        print("1. Go to Telegram")
        print("2. Find your bot by username: @" + bot_info.get('username', 'your_bot'))
        print("3. Send any message to the bot (e.g., 'hello')")
        print("4. Run this script again")
        sys.exit(1)
    
    print(f"✅ Chat ID found: {chat_id}")
    
    # Update configuration
    print("\n💾 Updating configuration...")
    if update_config(bot_token, chat_id):
        print("✅ Setup complete!")
        print()
        print("Next steps:")
        print("1. Test the configuration: python main.py --test")
        print("2. Run a price check: python main.py")
        print("3. Set up scheduling with: ./cron_setup.sh")
    else:
        print("❌ Failed to update configuration")
        sys.exit(1)

if __name__ == "__main__":
    main()
