Development Plan – Price Tracker with Telegram Notifications

1. Core Concept

Build a backend script that:
	•	Checks a list of product URLs once a day.
	•	Scrapes the current price.
	•	Compares it to the previously saved price.
	•	Sends a Telegram notification if the price has dropped.

⸻

2. Tech Stack
	•	Language: Python 3.x
	•	Scraping:
	•	requests + BeautifulSoup (for static pages)
	•	playwright or selenium (for dynamic/JavaScript-heavy pages)
	•	Database: SQLite (local) or Firebase (cloud)
	•	Notifications: Telegram Bot API
	•	Scheduling:
	•	Local: cron (Linux/macOS) / Task Scheduler (Windows)
	•	Cloud: GitHub Actions / Railway / Render Cron Jobs

⸻

3. Step-by-Step Implementation

Step 1: Set up the Telegram Bot
	1.	Go to Telegram → search for BotFather.
	2.	Run /newbot, choose a name and username.
	3.	Save the generated Bot Token.
	4.	Get your chat ID:
	•	Send any message to your bot.
	•	Visit: https://api.telegram.org/bot<YOUR_TOKEN>/getUpdates and copy your chat ID.

⸻

Step 2: Create the Scraper
	1.	Install dependencies:
    pip install requests beautifulsoup4



	2.	Write a function to fetch and parse the product price:
    import requests from bs4 import BeautifulSoup

def get_price(url):
    headers = {"User-Agent": "Mozilla/5.0"}
    html = requests.get(url, headers=headers)
    soup = BeautifulSoup(html.text, "html.parser")
    price_element = soup.select_one("CSS_SELECTOR_FOR_PRICE")
    return float(price_element.text.strip().replace("€", "").replace(",", "."))

3.	Test with one product URL. ie : https://www.bike-discount.de/en/radon-deft-10.0-800

Step 3: Store and Compare Prices
	1.	Create a SQLite DB to store:
	•	URL
	•	Last known price
	•	Product name
	2.	Each run:
	•	Fetch current price.
	•	Compare with the stored price.
	•	If lower → send notification + update DB.

⸻

Step 4: Send Telegram Notification

import requests

def send_telegram_message(token, chat_id, message):
    requests.post(f"https://api.telegram.org/bot{token}/sendMessage",
                  data={"chat_id": chat_id, "text": message})


Step 5: Schedule the Script
	•	Local dev: Add to cron (Linux/macOS):
    crontab -e
0 9 * * * /usr/bin/python3 /path/to/script.py

	•	Cloud option: Use GitHub Actions with a daily trigger.

4. Future Improvements
	•	Add support for multiple sites with different scraping logic.
	•	Track historical price trends and show charts.
	•	Build a small web dashboard (Flask/FastAPI + React/Flutter Web).
	•	Implement email notifications as an alternative.

⸻

