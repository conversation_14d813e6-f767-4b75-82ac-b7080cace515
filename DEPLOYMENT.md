# Deployment Guide

This guide explains how to deploy and schedule the Price Tracker application.

## Local Deployment (Recommended for personal use)

### 1. Setup Cron Job (Linux/macOS)

The easiest way to schedule daily price checks:

```bash
# Make the setup script executable
chmod +x cron_setup.sh

# Run the setup script
./cron_setup.sh
```

This will:
- Create a cron job that runs daily at 9:00 AM
- Log output to `price_tracker_cron.log`
- Allow you to easily manage the scheduled task

### 2. Manual Cron Setup

If you prefer to set up cron manually:

```bash
# Edit crontab
crontab -e

# Add this line for daily 9 AM runs:
0 9 * * * cd /path/to/price-tracker && /usr/bin/python3 main.py >> price_tracker_cron.log 2>&1
```

### 3. Windows Task Scheduler

For Windows users:

1. Open Task Scheduler
2. Create Basic Task
3. Set trigger to "Daily" at your preferred time
4. Set action to start a program:
   - Program: `python`
   - Arguments: `main.py`
   - Start in: `C:\path\to\price-tracker`

## Cloud Deployment

### GitHub Actions (Free tier available)

1. Fork this repository to your GitHub account

2. Set up repository secrets:
   - Go to Settings → Secrets and variables → Actions
   - Add these secrets:
     - `TELEGRAM_BOT_TOKEN`: Your Telegram bot token
     - `TELEGRAM_CHAT_ID`: Your Telegram chat ID

3. The workflow will run automatically daily at 9:00 AM UTC

4. To change the schedule, edit `.github/workflows/price-check.yml`:
   ```yaml
   schedule:
     - cron: '0 9 * * *'  # Change this line
   ```

### Railway/Render (Paid services)

1. Connect your GitHub repository
2. Set environment variables:
   - `TELEGRAM_BOT_TOKEN`
   - `TELEGRAM_CHAT_ID`
3. Set up cron job in their dashboard

## Configuration for Deployment

### Environment Variables

For cloud deployment, you can use environment variables instead of `config.json`:

```python
# Add this to your config.py if needed
import os

telegram_config = {
    'bot_token': os.getenv('TELEGRAM_BOT_TOKEN', ''),
    'chat_id': os.getenv('TELEGRAM_CHAT_ID', '')
}
```

### Database Persistence

- **Local**: SQLite database is stored locally
- **GitHub Actions**: Database is cached between runs
- **Cloud services**: Consider using cloud databases for persistence

## Monitoring

### Logs

- Local: Check `price_tracker.log` and `price_tracker_cron.log`
- GitHub Actions: View logs in the Actions tab
- Cloud services: Check their logging dashboards

### Telegram Notifications

The app sends:
- Price drop alerts (immediate)
- Daily summary notifications
- Error notifications (if configured)

### Health Checks

Run a test to verify everything is working:

```bash
python main.py --test
```

## Troubleshooting

### Common Issues

1. **Cron job not running**:
   - Check cron service: `sudo service cron status`
   - Verify cron job: `crontab -l`
   - Check logs: `tail -f price_tracker_cron.log`

2. **Telegram notifications not working**:
   - Verify bot token and chat ID
   - Test with: `python main.py --test`
   - Check bot permissions

3. **Scraping failures**:
   - Website might have changed structure
   - Update CSS selectors in config.json
   - Check for rate limiting

4. **Database issues**:
   - Ensure write permissions in directory
   - Check disk space
   - Backup database regularly

### Getting Help

1. Run with verbose logging: `python main.py --verbose`
2. Check the logs for detailed error messages
3. Test individual components with `--test` flag
