# Price Tracker with Telegram Notifications

A Python application that monitors product prices on various websites and sends Telegram notifications when prices drop.

## ✨ Features

- 📊 Daily price monitoring for multiple products
- 📱 Telegram notifications for price drops
- 💾 SQLite database for price history
- 🔧 Configurable CSS selectors for different websites
- ⚙️ Easy setup and deployment
- 🤖 Automated scheduling with cron or GitHub Actions
- 📈 Price drop percentage calculations
- 📝 Comprehensive logging

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Test the System
```bash
python test_complete_system.py
```

### 3. Set up Telegram Bot
```bash
python setup_telegram.py
```

### 4. Test Configuration
```bash
python main.py --test
```

### 5. Run Price Check
```bash
python main.py
```

## 📋 Detailed Setup

### Telegram Bot Setup
1. Go to Telegram and search for **BotFather**
2. Send `/newbot` and follow the instructions
3. Save the bot token you receive
4. Send any message to your new bot
5. Run `python setup_telegram.py` to configure automatically

### Manual Configuration
Edit `config.json` to customize:

```json
{
  "telegram": {
    "bot_token": "YOUR_BOT_TOKEN",
    "chat_id": "YOUR_CHAT_ID"
  },
  "products": [
    {
      "name": "Product Name",
      "url": "https://example.com/product",
      "price_selector": ".price",
      "currency": "€"
    }
  ]
}
```

## 🕐 Scheduling

### Local Scheduling (Recommended)
```bash
# Automated setup
./cron_setup.sh

# Manual setup
crontab -e
# Add: 0 9 * * * cd /path/to/price-tracker && python3 main.py
```

### Cloud Scheduling
- **GitHub Actions**: Included workflow runs daily
- **Railway/Render**: Use their cron job features

## 🧪 Testing

The application includes comprehensive testing:

- `test_complete_system.py` - Full system test without Telegram
- `test_scraper.py` - Test price scraping functionality
- `python main.py --test` - Test configuration and components

## 📁 Project Structure

```
price-tracker/
├── src/
│   ├── config.py           # Configuration management
│   ├── database.py         # SQLite database operations
│   ├── scraper.py          # Price scraping logic
│   ├── telegram_notifier.py # Telegram notifications
│   └── price_tracker.py    # Main application logic
├── main.py                 # Entry point
├── config.json            # Configuration file
├── requirements.txt       # Python dependencies
├── setup_telegram.py      # Telegram setup helper
├── test_complete_system.py # System tests
├── cron_setup.sh          # Cron job setup
└── DEPLOYMENT.md          # Deployment guide
```
